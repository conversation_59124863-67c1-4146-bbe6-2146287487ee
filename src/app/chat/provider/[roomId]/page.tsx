import React from "react";
import ProviderChatPage from "@/components/chat/provider-chat-page";

export const dynamic = "force-dynamic";
export const fetchCache = "force-no-store";

interface Props {
  params: {
    roomId: string;
  };
}

/**
 * Provider Chat Page Route
 * Handles provider-specific chat functionality with authentication wrapper
 */
const ProviderChatRoute = ({ params }: Props) => {
  return <ProviderChatPage roomId={params.roomId} />;
};

export default ProviderChatRoute;
