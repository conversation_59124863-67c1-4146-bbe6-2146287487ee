import React from "react";
import PatientChatPage from "@/components/chat/patient-chat-page";

export const dynamic = "force-dynamic";
export const fetchCache = "force-no-store";

interface Props {
  params: {
    roomId: string;
  };
}

/**
 * Patient Chat Page Route
 * Handles patient-specific chat functionality with authentication wrapper
 */
const PatientChatRoute = ({ params }: Props) => {
  return <PatientChatPage roomId={params.roomId} />;
};

export default PatientChatRoute;
