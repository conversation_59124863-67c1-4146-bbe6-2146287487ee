"use client";
import React, { useState, useEffect } from "react";
import { useSearchParams } from "next/navigation";
import { toast } from "sonner";
import { tokenManager } from "@/lib/tokenManager";
import { useAuth } from "@/hooks/useAuth";
import { OtpModal } from "./otp-modal";
import { Loader } from "./loader";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";

interface AuthWrapperProps {
  children: React.ReactNode;
  roomId?: string;
}

/**
 * Authentication Wrapper Component
 * Checks for accessToken in cookies, validates token expiration,
 * and displays login page with email+OTP flow when not authenticated
 */
const AuthWrapper: React.FC<AuthWrapperProps> = ({ children, roomId }) => {
  const [isLoading, setIsLoading] = useState(true);
  const [showOtpModal, setShowOtpModal] = useState(false);
  const [showEmailForm, setShowEmailForm] = useState(false);
  const [email, setEmail] = useState("");
  const [isEmailLoading, setIsEmailLoading] = useState(false);
  const [userGuid, setUserGuid] = useState("");

  const { isAuthenticated, verifyOtp, sendOtp, token } = useAuth();
  const searchParams = useSearchParams();

  // Auto-fill email from URL params
  useEffect(() => {
    const emailParam = searchParams.get("email");
    if (emailParam) {
      setEmail(emailParam);
    }
  }, [searchParams]);

  // Check authentication status on mount
  useEffect(() => {
    checkAuthStatus();
  }, []);

  const checkAuthStatus = async () => {
    setIsLoading(true);
    
    try {
      // Check for token from URL params first
      const tokenParam = searchParams.get("token");
      if (tokenParam) {
        await tokenManager.setToken(tokenParam);
      }

      // Get current token
      const currentToken = await tokenManager.getTokenWithMigration();
      
      if (!currentToken) {
        // No token found, show email form
        setShowEmailForm(true);
      } else {
        // Token exists, check if it's valid by making a test API call
        // If the token is expired, the API will return an error
        // and we'll handle it in the child components
      }
    } catch (error) {
      console.error("Auth check failed:", error);
      setShowEmailForm(true);
    } finally {
      setIsLoading(false);
    }
  };

  const handleEmailSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!email.trim()) {
      toast.error("Please enter your email address");
      return;
    }

    setIsEmailLoading(true);
    
    try {
      // Send OTP to email
      const success = await sendOtp(email);
      if (success) {
        setUserGuid(email); // Use email as user identifier
        setShowEmailForm(false);
        setShowOtpModal(true);
      }
    } catch (error) {
      console.error("Failed to send OTP:", error);
      toast.error("Failed to send OTP. Please try again.");
    } finally {
      setIsEmailLoading(false);
    }
  };

  const handleOtpVerification = async (otp: string): Promise<boolean> => {
    try {
      const success = await verifyOtp(userGuid, otp);
      if (success) {
        setShowOtpModal(false);
        return true;
      }
      return false;
    } catch (error) {
      console.error("OTP verification failed:", error);
      return false;
    }
  };

  const handleResendOtp = async (): Promise<void> => {
    try {
      await sendOtp(userGuid);
    } catch (error) {
      console.error("Failed to resend OTP:", error);
      throw error;
    }
  };

  // Show loading state
  if (isLoading) {
    return <Loader show={true} />;
  }

  // Show email form if not authenticated
  if (showEmailForm) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
        <Card className="w-full max-w-md">
          <CardHeader className="text-center">
            <CardTitle className="text-2xl font-bold">Welcome to Chat</CardTitle>
            <CardDescription>
              Enter your email address to continue
            </CardDescription>
          </CardHeader>
          <CardContent>
            <form onSubmit={handleEmailSubmit} className="space-y-4">
              <div>
                <Input
                  type="email"
                  placeholder="Enter your email address"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  disabled={isEmailLoading}
                  required
                  autoFocus
                />
              </div>
              <Button 
                type="submit" 
                className="w-full" 
                disabled={isEmailLoading || !email.trim()}
              >
                {isEmailLoading ? "Sending OTP..." : "Send OTP"}
              </Button>
            </form>
          </CardContent>
        </Card>
      </div>
    );
  }

  // Show OTP modal if needed
  if (showOtpModal) {
    return (
      <OtpModal
        isOpen={showOtpModal}
        onClose={() => {
          setShowOtpModal(false);
          setShowEmailForm(true);
        }}
        onVerify={handleOtpVerification}
        onResendOtp={handleResendOtp}
        userEmail={email}
        title="Verify Your Email"
        description="Please enter the OTP sent to your email address."
      />
    );
  }

  // If authenticated, render children
  if (isAuthenticated || token) {
    return <>{children}</>;
  }

  // Fallback to email form
  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50">
      <Card className="w-full max-w-md">
        <CardHeader className="text-center">
          <CardTitle>Authentication Required</CardTitle>
          <CardDescription>
            Please authenticate to access the chat
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Button 
            onClick={() => setShowEmailForm(true)}
            className="w-full"
          >
            Continue to Login
          </Button>
        </CardContent>
      </Card>
    </div>
  );
};

export default AuthWrapper;
