"use client";
import React, { useState, useEffect } from "react";
import { getCookie } from "@/actions/cookies";
import { apiClient } from "@/lib/api";
import { tokenManager } from "@/lib/tokenManager";
import AuthWrapper from "@/components/shared/auth-wrapper";
import UnifiedChatComponent from "./unified-chat";
import ErrorRendered from "@/components/shared/error/error-renderer";
import { Loader } from "@/components/shared/loader";
import CONSTANT from "@/lib/constant";
import { toast } from "sonner";

interface ProviderChatPageProps {
  roomId: string;
}

interface ChatData {
  patient: any;
  provider: any;
  room: any;
  messages?: any[];
}

/**
 * Provider Chat Page Component
 * Handles provider-specific chat functionality with authentication wrapper
 */
const ProviderChatPage: React.FC<ProviderChatPageProps> = ({ roomId }) => {
  const [chatData, setChatData] = useState<ChatData | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    loadChatData();
  }, [roomId]);

  const loadChatData = async () => {
    setIsLoading(true);
    setError(null);

    try {
      // Get token from cookies
      const token = await tokenManager.getTokenWithMigration();
      
      if (!token) {
        // Let AuthWrapper handle authentication
        setIsLoading(false);
        return;
      }

      // Load chat metadata
      const response = await apiClient({
        method: "GET",
        endpoint: `chat/${roomId}/metadata`,
        token,
      });

      if (response.statusCode === 200 && response.data) {
        setChatData(response.data);
      } else if (response.statusCode === 401 || response.statusCode === 403) {
        // Token expired or invalid, clear it and let AuthWrapper handle re-auth
        await tokenManager.removeToken();
        setError("Authentication expired. Please log in again.");
      } else {
        setError(response.message || "Failed to load chat data");
      }
    } catch (err: any) {
      console.error("Failed to load chat data:", err);
      setError("Failed to load chat data");
    } finally {
      setIsLoading(false);
    }
  };

  const renderChatContent = () => {
    if (isLoading) {
      return <Loader show={true} />;
    }

    if (error) {
      return (
        <div className="min-h-screen flex items-center justify-center">
          <div className="text-center">
            <h2 className="text-xl font-semibold text-red-600 mb-2">Error</h2>
            <p className="text-gray-600 mb-4">{error}</p>
            <button
              onClick={loadChatData}
              className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
            >
              Retry
            </button>
          </div>
        </div>
      );
    }

    if (!chatData) {
      return (
        <div className="min-h-screen flex items-center justify-center">
          <div className="text-center">
            <h2 className="text-xl font-semibold text-gray-600">No chat data available</h2>
            <p className="text-gray-500 mt-2">Please check the room ID and try again.</p>
          </div>
        </div>
      );
    }

    return (
      <UnifiedChatComponent
        patient={chatData.patient}
        provider={chatData.provider}
        room={chatData.room}
        role="PROVIDER"
      />
    );
  };

  return (
    <AuthWrapper roomId={roomId}>
      {renderChatContent()}
    </AuthWrapper>
  );
};

export default ProviderChatPage;
